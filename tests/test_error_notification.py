"""Tests for enhanced error notification system."""
import pytest
import time
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from fastapi import Request
from fastapi.responses import JSONResponse

from src.infrastructure.services.error_notification_service import ErrorNotificationService
from src.infrastructure.utils.request_helper import (
    extract_request_details,
    extract_response_details,
    extract_bot_token,
    extract_user_chat_id,
    get_client_ip,
    sanitize_headers,
    truncate_string
)


class TestErrorNotificationService:
    """Test cases for ErrorNotificationService."""

    @pytest.fixture
    def error_service(self):
        """Create ErrorNotificationService instance for testing."""
        return ErrorNotificationService("test_bot_token")

    @pytest.fixture
    def mock_request(self):
        """Create a mock FastAPI request."""
        request = MagicMock(spec=Request)
        request.method = "POST"
        request.url.path = "/api/download"
        request.url = MagicMock()
        request.url.__str__ = MagicMock(return_value="http://localhost:8000/api/download")
        request.headers = {
            "user-agent": "Mozilla/5.0",
            "x-bot-token": "test_token",
            "x-chat-id": "123456",
            "authorization": "Bearer secret_token"
        }
        request.query_params = {"param1": "value1"}
        request.body = MagicMock(return_value=b'{"test": "data"}')
        request.client.host = "127.0.0.1"
        return request

    @pytest.fixture
    def mock_response(self):
        """Create a mock FastAPI response."""
        response = MagicMock()
        response.status_code = 200
        response.headers = {"content-type": "application/json"}
        response.body = b'{"status": "success"}'
        return response

    def test_extract_request_details(self, mock_request):
        """Test extracting request details."""
        details = extract_request_details(mock_request)
        
        assert details['method'] == "POST"
        assert details['endpoint'] == "/api/download"
        assert details['url'] == "http://localhost:8000/api/download"
        assert details['params'] == {"param1": "value1"}
        assert details['client_ip'] == "127.0.0.1"
        assert details['user_agent'] == "Mozilla/5.0"
        assert 'body' in details

    def test_extract_response_details(self, mock_response):
        """Test extracting response details."""
        details = extract_response_details(mock_response)
        
        assert details['status_code'] == 200
        assert details['headers'] == {"content-type": "application/json"}
        assert 'response_body' in details

    def test_extract_bot_token_from_headers(self, mock_request):
        """Test extracting bot token from headers."""
        token = extract_bot_token(mock_request)
        assert token == "test_token"

    def test_extract_bot_token_from_query_params(self):
        """Test extracting bot token from query parameters."""
        request = MagicMock(spec=Request)
        request.headers = {}
        request.query_params = {"bot_token": "query_token"}
        request.method = "GET"
        
        token = extract_bot_token(request)
        assert token == "query_token"

    def test_extract_user_chat_id_from_headers(self, mock_request):
        """Test extracting user chat ID from headers."""
        chat_id = extract_user_chat_id(mock_request)
        assert chat_id == "123456"

    def test_get_client_ip(self, mock_request):
        """Test getting client IP address."""
        ip = get_client_ip(mock_request)
        assert ip == "127.0.0.1"

    def test_get_client_ip_with_forwarded_header(self):
        """Test getting client IP from forwarded header."""
        request = MagicMock(spec=Request)
        request.headers = {"X-Forwarded-For": "***********, ********"}
        request.client = None
        
        ip = get_client_ip(request)
        assert ip == "***********"

    def test_sanitize_headers(self):
        """Test sanitizing sensitive headers."""
        headers = {
            "user-agent": "Mozilla/5.0",
            "authorization": "Bearer secret",
            "x-api-key": "secret_key",
            "content-type": "application/json"
        }
        
        sanitized = sanitize_headers(headers)
        
        assert "authorization" not in sanitized
        assert "x-api-key" not in sanitized
        assert "user-agent" in sanitized
        assert "content-type" in sanitized

    def test_truncate_string(self):
        """Test string truncation."""
        long_string = "a" * 300
        truncated = truncate_string(long_string, max_length=200)
        
        assert len(truncated) == 203  # 200 + "..."
        assert truncated.endswith("...")

    def test_truncate_string_short(self):
        """Test string truncation with short string."""
        short_string = "short"
        result = truncate_string(short_string, max_length=200)
        
        assert result == short_string
        assert len(result) == 5

    @patch('src.infrastructure.services.error_notification_service.Bot')
    async def test_notify_admin_error_with_details(self, mock_bot, error_service, mock_request, mock_response):
        """Test sending detailed error notification."""
        # Mock bot
        mock_bot_instance = AsyncMock()
        mock_bot.return_value = mock_bot_instance
        
        # Mock settings
        with patch('src.infrastructure.services.error_notification_service.settings') as mock_settings:
            mock_settings.admin.chat_id = "admin_chat_id"
            mock_settings.admin.enable_error_notifications = True
            
            # Test notification
            request_details = extract_request_details(mock_request)
            response_details = extract_response_details(mock_response)
            
            await error_service.notify_admin_error(
                error_message="Test error",
                error_type="TEST_ERROR",
                context={"test": "context"},
                user_chat_id="123456",
                request_details=request_details,
                response_details=response_details,
                execution_time=1.5,
                stack_trace="Traceback (most recent call last):\n  File test.py, line 1, in <module>\n    raise Exception('test')"
            )
            
            # Verify bot was called
            mock_bot_instance.send_message.assert_called_once()
            
            # Verify message contains all details
            call_args = mock_bot_instance.send_message.call_args
            message = call_args[1]['text']
            
            assert "Test error" in message
            assert "TEST_ERROR" in message
            assert "1.50s" in message  # Execution time
            assert "POST" in message  # Request method
            assert "200" in message  # Response status
            assert "Traceback" in message  # Stack trace

    def test_classify_error(self, error_service):
        """Test error classification."""
        # Test network error
        network_error = Exception("Connection timeout")
        assert error_service.classify_error(network_error) == "NETWORK_ERROR"
        
        # Test validation error
        validation_error = ValueError("Invalid format")
        assert error_service.classify_error(validation_error) == "VALIDATION_ERROR"
        
        # Test Instagram error
        instagram_error = Exception("Instagram API error")
        assert error_service.classify_error(instagram_error) == "INSTAGRAM_API_ERROR"
        
        # Test default error
        unknown_error = Exception("Unknown error")
        assert error_service.classify_error(unknown_error) == "SERVER_ERROR"

    def test_get_user_friendly_message(self, error_service):
        """Test getting user-friendly error messages."""
        message = error_service.get_user_friendly_message("NETWORK_ERROR", "Connection failed")
        assert "Internet aloqasida muammo" in message
        
        message = error_service.get_user_friendly_message("VALIDATION_ERROR", "Invalid input")
        assert "Noto'g'ri ma'lumot kiritildi" in message
        
        message = error_service.get_user_friendly_message("UNKNOWN_ERROR", "Unknown")
        assert "Kutilmagan xatolik yuz berdi" in message


class TestRequestHelperIntegration:
    """Integration tests for request helper functions."""

    def test_full_request_processing(self):
        """Test complete request processing workflow."""
        # Create mock request
        request = MagicMock(spec=Request)
        request.method = "POST"
        request.url.path = "/api/download"
        request.url.__str__ = MagicMock(return_value="http://localhost:8000/api/download")
        request.headers = {
            "user-agent": "TestBot/1.0",
            "x-bot-token": "bot_token_123",
            "x-chat-id": "987654",
            "authorization": "Bearer secret_token",
            "content-type": "application/json"
        }
        request.query_params = {"format": "mp4"}
        request.body = MagicMock(return_value=b'{"url": "https://example.com/video"}')
        request.client.host = "***********00"
        
        # Extract all details
        request_details = extract_request_details(request)
        bot_token = extract_bot_token(request)
        user_chat_id = extract_user_chat_id(request)
        client_ip = get_client_ip(request)
        sanitized_headers = sanitize_headers(request_details['headers'])
        
        # Verify results
        assert request_details['method'] == "POST"
        assert request_details['endpoint'] == "/api/download"
        assert bot_token == "bot_token_123"
        assert user_chat_id == "987654"
        assert client_ip == "***********00"
        assert "authorization" not in sanitized_headers
        assert "x-bot-token" not in sanitized_headers
        assert "content-type" in sanitized_headers 