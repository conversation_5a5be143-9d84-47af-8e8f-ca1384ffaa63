"""Interface for error notification service."""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any


class IErrorNotificationService(ABC):
    """Interface for error notification service."""

    @abstractmethod
    async def notify_admin_error(
        self,
        error_message: str,
        error_type: str,
        context: Optional[dict] = None,
        user_chat_id: Optional[str] = None,
        request_details: Optional[Dict[str, Any]] = None,
        response_details: Optional[Dict[str, Any]] = None,
        execution_time: Optional[float] = None,
        stack_trace: Optional[str] = None
    ) -> None:
        """
        Notify admin about an error with detailed information.
        
        Args:
            error_message: The error message
            error_type: Type of error (e.g., 'API_ERROR', 'VALIDATION_ERROR', etc.)
            context: Additional context information
            user_chat_id: Chat ID where the error occurred (if applicable)
            request_details: Details about the request that caused the error
            response_details: Details about the response (if any)
            execution_time: Time taken to process the request
            stack_trace: Full stack trace of the error
        """
        pass

    @abstractmethod
    def get_user_friendly_message(self, error_type: str, original_error: str) -> str:
        """
        Get user-friendly error message in Uzbek.
        
        Args:
            error_type: Type of error
            original_error: Original error message
            
        Returns:
            User-friendly error message in Uzbek
        """
        pass

    @abstractmethod
    def classify_error(self, error: Exception) -> str:
        """
        Classify error type based on exception.
        
        Args:
            error: The exception that occurred
            
        Returns:
            Error type classification
        """
        pass

    @abstractmethod
    def extract_request_details(self, request) -> Dict[str, Any]:
        """
        Extract request details from FastAPI request object.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Dictionary containing request details
        """
        pass

    @abstractmethod
    def extract_response_details(self, response) -> Dict[str, Any]:
        """
        Extract response details from FastAPI response object.
        
        Args:
            response: FastAPI response object
            
        Returns:
            Dictionary containing response details
        """
        pass
