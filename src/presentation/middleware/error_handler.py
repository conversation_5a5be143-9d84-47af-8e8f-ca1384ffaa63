"""Global error handler middleware for FastAPI."""
import logging
import time
import traceback
from typing import Union
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.presentation.models.response_models import ErrorResponse
from src.presentation.dependencies import get_error_notification_service
from src.infrastructure.utils.request_helper import (
    extract_request_details,
    extract_response_details,
    extract_bot_token,
    extract_user_chat_id,
    get_client_ip,
    sanitize_headers
)


logger = logging.getLogger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware to handle all unhandled exceptions with detailed logging."""

    async def dispatch(self, request: Request, call_next):
        """Process request and handle any exceptions with detailed information."""
        start_time = time.time()
        request_details = None
        response_details = None
        execution_time = None
        
        try:
            # Extract request details before processing
            request_details = extract_request_details(request)
            
            # Process the request
            response = await call_next(request)
            
            # Calculate execution time
            execution_time = time.time() - start_time
            
            # Extract response details
            response_details = extract_response_details(response)
            
            return response
            
        except HTTPException:
            # Let FastAPI handle HTTP exceptions normally
            execution_time = time.time() - start_time
            raise
        except Exception as e:
            # Handle unexpected exceptions with detailed logging
            execution_time = time.time() - start_time
            stack_trace = traceback.format_exc()
            
            logger.error(f"Unhandled exception in {request.url.path}: {e}", exc_info=True)
            
            # Try to get bot token from request for error notification
            bot_token = extract_bot_token(request)
            
            # Send detailed error notification to admin if bot_token is available
            if bot_token:
                try:
                    error_service = get_error_notification_service(bot_token)
                    error_type = error_service.classify_error(e)
                    
                    # Get user chat ID if available
                    user_chat_id = extract_user_chat_id(request)
                    
                    # Sanitize headers before sending
                    if request_details and 'headers' in request_details:
                        request_details['headers'] = sanitize_headers(request_details['headers'])
                    
                    await error_service.notify_admin_error(
                        error_message=str(e),
                        error_type=error_type,
                        context={
                            "path": str(request.url.path),
                            "method": request.method,
                            "user_agent": request.headers.get("user-agent", "Unknown"),
                            "ip_address": get_client_ip(request),
                            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                        },
                        user_chat_id=user_chat_id,
                        request_details=request_details,
                        response_details=response_details,
                        execution_time=execution_time,
                        stack_trace=stack_trace
                    )
                except Exception as notification_error:
                    logger.error(f"Failed to send error notification: {notification_error}")

            # Return user-friendly error response
            return JSONResponse(
                status_code=500,
                content=ErrorResponse(
                    status="error",
                    message="Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
                ).model_dump()
            )


async def handle_validation_error(request: Request, exc: Exception) -> JSONResponse:
    """Handle validation errors with detailed logging."""
    logger.warning(f"Validation error in {request.url.path}: {exc}")
    
    # Try to send detailed notification for validation errors
    try:
        bot_token = extract_bot_token(request)
        if bot_token:
            error_service = get_error_notification_service(bot_token)
            user_chat_id = extract_user_chat_id(request)
            
            # Extract request details
            request_details = extract_request_details(request)
            if request_details and 'headers' in request_details:
                request_details['headers'] = sanitize_headers(request_details['headers'])
            
            await error_service.notify_admin_error(
                error_message=str(exc),
                error_type="VALIDATION_ERROR",
                context={
                    "endpoint": request.url.path,
                    "method": request.method,
                    "user_agent": request.headers.get("user-agent", "Unknown"),
                    "ip_address": get_client_ip(request)
                },
                user_chat_id=user_chat_id,
                request_details=request_details,
                execution_time=0.0,
                stack_trace=traceback.format_exc()
            )
    except Exception as notification_error:
        logger.error(f"Failed to send validation error notification: {notification_error}")
    
    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            status="error",
            message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting.",
            detail=str(exc)
        ).model_dump()
    )


async def handle_http_exception(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions with detailed logging."""
    logger.warning(f"HTTP exception in {request.url.path}: {exc.status_code} - {exc.detail}")
    
    # Try to send detailed notification for HTTP errors
    try:
        bot_token = extract_bot_token(request)
        if bot_token and exc.status_code >= 500:  # Only notify for server errors
            error_service = get_error_notification_service(bot_token)
            user_chat_id = extract_user_chat_id(request)
            
            # Extract request details
            request_details = extract_request_details(request)
            if request_details and 'headers' in request_details:
                request_details['headers'] = sanitize_headers(request_details['headers'])
            
            await error_service.notify_admin_error(
                error_message=str(exc.detail),
                error_type="HTTP_ERROR",
                context={
                    "endpoint": request.url.path,
                    "method": request.method,
                    "status_code": exc.status_code,
                    "user_agent": request.headers.get("user-agent", "Unknown"),
                    "ip_address": get_client_ip(request)
                },
                user_chat_id=user_chat_id,
                request_details=request_details,
                response_details={
                    'status_code': exc.status_code
                },
                execution_time=0.0,
                stack_trace=traceback.format_exc()
            )
    except Exception as notification_error:
        logger.error(f"Failed to send HTTP error notification: {notification_error}")
    
    # Map HTTP status codes to user-friendly messages
    user_messages = {
        400: "Noto'g'ri so'rov. Iltimos, ma'lumotlarni tekshiring.",
        401: "Ruxsat berilmagan. Iltimos, qayta kirish qiling.",
        403: "Ruxsat yo'q. Ushbu amalni bajarish uchun huquqingiz yo'q.",
        404: "Sahifa topilmadi. Iltimos, to'g'ri manzilni kiriting.",
        429: "Juda ko'p so'rov. Iltimos, biroz kuting.",
        500: "Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
        502: "Server javob bermayapti. Iltimos, keyinroq qayta urinib ko'ring.",
        503: "Servis vaqtincha ishlamayapti. Iltimos, keyinroq qayta urinib ko'ring."
    }
    
    user_message = user_messages.get(exc.status_code, "Kutilmagan xatolik yuz berdi.")
    
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            status="error",
            message=user_message,
            detail=str(exc.detail) if exc.status_code < 500 else None
        ).model_dump()
    )
