"""Media download controller."""
import logging
import time
import traceback
from datetime import datetime
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse

from src.presentation.models.request_models import DownloadRequest, SearchMusicRequest, DownloadFromShortcodeRequest
from src.presentation.models.response_models import DownloadResponse, ErrorResponse, HealthResponse, SearchMusicResponse
from src.application.dtos.download_dtos import DownloadMediaRequest
from src.application.dtos.search_dtos import SearchMusicRequest as SearchMusicRequestDto, DownloadFromShortcodeRequest as DownloadFromShortcodeRequestDto
from src.presentation.dependencies import (
    get_download_media_use_case,
    get_search_music_use_case,
    get_download_from_shortcode_use_case,
    get_music_cache_repository,
    get_error_notification_service
)
from src.infrastructure.utils.request_helper import (
    extract_request_details,
    extract_response_details,
    extract_bot_token,
    extract_user_chat_id,
    sanitize_headers
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["media"])


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        timestamp=datetime.now().isoformat()
    )


@router.post("/download", response_model=DownloadResponse)
async def download_media(request: DownloadRequest, http_request: Request):
    """Download media from Instagram or YouTube and send to Telegram."""
    start_time = time.time()
    request_details = None
    response_details = None
    execution_time = None
    
    try:
        logger.info(f"Received download request for URL: {request.url}")

        # Extract request details using utility function
        request_details = extract_request_details(http_request)
        
        # Add specific request body details
        if request_details and 'body' not in request_details:
            request_details['body'] = {
                'chat_id': request.chat_id,
                'url': request.url,
                'media_type': request.media_type.value,
                'video_format': request.video_format
            }

        # Get use case with proper dependencies
        use_case = get_download_media_use_case(request.bot_token)

        # Convert to application DTO
        app_request = DownloadMediaRequest(
            chat_id=request.chat_id,
            url=request.url,
            bot_token=request.bot_token,
            media_type=request.media_type.value,
            video_format=request.video_format
        )

        # Execute use case
        result = await use_case.execute(app_request)
        
        # Calculate execution time
        execution_time = time.time() - start_time

        # Convert to presentation response
        if result.success:
            response = DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )
            
            # Extract response details
            response_details = {
                'status_code': 200,
                'response_body': response.model_dump()
            }
            
            return response
        else:
            response = JSONResponse(
                status_code=400,
                content=ErrorResponse(
                    status="error",
                    message=result.message
                ).model_dump()
            )
            
            # Extract response details
            response_details = {
                'status_code': 400,
                'response_body': result.message
            }
            
            return response

    except ValueError as e:
        execution_time = time.time() - start_time
        stack_trace = traceback.format_exc()
        logger.error(f"Validation error: {e}")

        # Get user-friendly error message
        try:
            error_service = get_error_notification_service(request.bot_token)
            user_message = error_service.get_user_friendly_message("VALIDATION_ERROR", str(e))

            # Sanitize headers before sending
            if request_details and 'headers' in request_details:
                request_details['headers'] = sanitize_headers(request_details['headers'])

            # Notify admin about validation error with detailed information
            await error_service.notify_admin_error(
                error_message=str(e),
                error_type="VALIDATION_ERROR",
                context={
                    "endpoint": "/download",
                    "url": request.url,
                    "chat_id": request.chat_id,
                    "media_type": request.media_type.value,
                    "video_format": request.video_format
                },
                user_chat_id=str(request.chat_id),
                request_details=request_details,
                response_details=response_details,
                execution_time=execution_time,
                stack_trace=stack_trace
            )
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")
            user_message = "Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=user_message
            ).model_dump()
        )
    except Exception as e:
        execution_time = time.time() - start_time
        stack_trace = traceback.format_exc()
        logger.error(f"Unexpected error in media controller: {e}")

        # Get user-friendly error message and notify admin
        try:
            error_service = get_error_notification_service(request.bot_token)
            error_type = error_service.classify_error(e)
            user_message = error_service.get_user_friendly_message(error_type, str(e))

            # Sanitize headers before sending
            if request_details and 'headers' in request_details:
                request_details['headers'] = sanitize_headers(request_details['headers'])

            # Notify admin about the error with detailed information
            await error_service.notify_admin_error(
                error_message=str(e),
                error_type=error_type,
                context={
                    "endpoint": "/download",
                    "url": request.url,
                    "chat_id": request.chat_id,
                    "media_type": request.media_type.value,
                    "video_format": request.video_format
                },
                user_chat_id=str(request.chat_id),
                request_details=request_details,
                response_details=response_details,
                execution_time=execution_time,
                stack_trace=stack_trace
            )
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")
            user_message = "Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message=user_message
            ).model_dump()
        )


@router.get("/search-music", response_model=SearchMusicResponse)
async def search_music(query: str, page: int = 1, http_request: Request = None):
    """Search for music using the FastSaver API."""
    start_time = time.time()
    request_details = None
    response_details = None
    execution_time = None
    
    try:
        logger.info(f"Received music search request for query: {query}, page: {page}")

        # Extract request details using utility function
        if http_request:
            request_details = extract_request_details(http_request)

        # Get use case
        use_case = get_search_music_use_case()

        # Convert to application DTO
        app_request = SearchMusicRequestDto(
            query=query,
            page=page
        )

        # Execute use case
        result = await use_case.execute(app_request)
        
        # Calculate execution time
        execution_time = time.time() - start_time

        # Convert to presentation response
        from src.presentation.models.response_models import MusicSearchResult
        search_results = [
            MusicSearchResult(
                title=item.title,
                shortcode=item.shortcode,
                duration=item.duration,
                thumb=item.thumb,
                thumb_best=item.thumb_best
            )
            for item in result.results
        ]

        response = SearchMusicResponse(
            error=result.error,
            page=result.page,
            results=search_results
        )
        
        # Extract response details
        response_details = {
            'status_code': 200,
            'response_body': response.model_dump()
        }

        return response

    except ValueError as e:
        execution_time = time.time() - start_time
        stack_trace = traceback.format_exc()
        logger.error(f"Validation error: {e}")
        
        # Try to send detailed notification for validation errors
        try:
            if http_request:
                bot_token = extract_bot_token(http_request)
                if bot_token:
                    error_service = get_error_notification_service(bot_token)
                    
                    # Sanitize headers before sending
                    if request_details and 'headers' in request_details:
                        request_details['headers'] = sanitize_headers(request_details['headers'])
                    
                    await error_service.notify_admin_error(
                        error_message=str(e),
                        error_type="VALIDATION_ERROR",
                        context={
                            "endpoint": "/search-music",
                            "query": query,
                            "page": page
                        },
                        request_details=request_details,
                        response_details=response_details,
                        execution_time=execution_time,
                        stack_trace=stack_trace
                    )
        except Exception as notification_error:
            logger.error(f"Failed to send validation error notification: {notification_error}")
        
        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."
            ).model_dump()
        )
    except Exception as e:
        execution_time = time.time() - start_time
        stack_trace = traceback.format_exc()
        logger.error(f"Unexpected error in search music: {e}")
        
        # Try to send detailed notification for unexpected errors
        try:
            if http_request:
                bot_token = extract_bot_token(http_request)
                if bot_token:
                    error_service = get_error_notification_service(bot_token)
                    error_type = error_service.classify_error(e)
                    
                    # Sanitize headers before sending
                    if request_details and 'headers' in request_details:
                        request_details['headers'] = sanitize_headers(request_details['headers'])
                    
                    await error_service.notify_admin_error(
                        error_message=str(e),
                        error_type=error_type,
                        context={
                            "endpoint": "/search-music",
                            "query": query,
                            "page": page
                        },
                        request_details=request_details,
                        response_details=response_details,
                        execution_time=execution_time,
                        stack_trace=stack_trace
                    )
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")
        
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Musiqa qidiruvida xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            ).model_dump()
        )


@router.post("/download-shortcode", response_model=DownloadResponse)
async def download_from_shortcode(request: DownloadFromShortcodeRequest, http_request: Request):
    """Download media from YouTube shortcode and send to Telegram."""
    start_time = time.time()
    request_details = None
    response_details = None
    execution_time = None
    
    try:
        logger.info(f"Received download from shortcode request: {request.shortcode}")

        # Extract request details using utility function
        request_details = extract_request_details(http_request)
        
        # Add specific request body details
        if request_details and 'body' not in request_details:
            request_details['body'] = {
                'chat_id': request.chat_id,
                'shortcode': request.shortcode,
                'media_type': request.media_type.value
            }

        # Get use case
        use_case = get_download_from_shortcode_use_case(request.bot_token)

        # Convert to application DTO
        app_request = DownloadFromShortcodeRequestDto(
            chat_id=request.chat_id,
            shortcode=request.shortcode,
            bot_token=request.bot_token,
            media_type=request.media_type.value
        )

        # Execute use case
        result = await use_case.execute(app_request)
        
        # Calculate execution time
        execution_time = time.time() - start_time

        # Convert to presentation response
        if result.success:
            response = DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )
            
            # Extract response details
            response_details = {
                'status_code': 200,
                'response_body': response.model_dump()
            }
            
            return response
        else:
            response = JSONResponse(
                status_code=400,
                content=ErrorResponse(
                    status="error",
                    message=result.message
                ).model_dump()
            )
            
            # Extract response details
            response_details = {
                'status_code': 400,
                'response_body': result.message
            }
            
            return response

    except ValueError as e:
        execution_time = time.time() - start_time
        stack_trace = traceback.format_exc()
        logger.error(f"Validation error: {e}")

        # Get user-friendly error message
        try:
            error_service = get_error_notification_service(request.bot_token)
            user_message = error_service.get_user_friendly_message("VALIDATION_ERROR", str(e))

            # Sanitize headers before sending
            if request_details and 'headers' in request_details:
                request_details['headers'] = sanitize_headers(request_details['headers'])

            # Notify admin about validation error with detailed information
            await error_service.notify_admin_error(
                error_message=str(e),
                error_type="VALIDATION_ERROR",
                context={
                    "endpoint": "/download-shortcode",
                    "shortcode": request.shortcode,
                    "chat_id": request.chat_id,
                    "media_type": request.media_type.value
                },
                user_chat_id=str(request.chat_id),
                request_details=request_details,
                response_details=response_details,
                execution_time=execution_time,
                stack_trace=stack_trace
            )
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")
            user_message = "Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=user_message
            ).model_dump()
        )
    except Exception as e:
        execution_time = time.time() - start_time
        stack_trace = traceback.format_exc()
        logger.error(f"Unexpected error in download from shortcode: {e}")

        # Get user-friendly error message and notify admin
        try:
            error_service = get_error_notification_service(request.bot_token)
            error_type = error_service.classify_error(e)
            user_message = error_service.get_user_friendly_message(error_type, str(e))

            # Sanitize headers before sending
            if request_details and 'headers' in request_details:
                request_details['headers'] = sanitize_headers(request_details['headers'])

            # Notify admin about the error with detailed information
            await error_service.notify_admin_error(
                error_message=str(e),
                error_type=error_type,
                context={
                    "endpoint": "/download-shortcode",
                    "shortcode": request.shortcode,
                    "chat_id": request.chat_id,
                    "media_type": request.media_type.value
                },
                user_chat_id=str(request.chat_id),
                request_details=request_details,
                response_details=response_details,
                execution_time=execution_time,
                stack_trace=stack_trace
            )
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")
            user_message = "Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message=user_message
            ).model_dump()
        )


@router.get("/cache-stats")
async def get_cache_stats():
    """Get music search cache statistics."""
    try:
        cache_repo = get_music_cache_repository()
        stats = await cache_repo.get_cache_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Kesh statistikasini olishda xatolik yuz berdi."
            ).model_dump()
        )


@router.post("/cleanup-cache")
async def cleanup_cache():
    """Cleanup expired cache entries."""
    try:
        cache_repo = get_music_cache_repository()
        await cache_repo.cleanup_expired_cache()
        return {"status": "success", "message": "Cache cleanup completed"}
    except Exception as e:
        logger.error(f"Error cleaning up cache: {e}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Keshni tozalashda xatolik yuz berdi."
            ).model_dump()
        )
