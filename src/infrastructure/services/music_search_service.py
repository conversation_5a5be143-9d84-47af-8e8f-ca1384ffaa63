"""Music search service implementation."""
import httpx
import logging
import re
from typing import Optional

from src.domain.interfaces.services import IMusicSearchService
from src.domain.interfaces.repositories import IMusicCacheRepository
from src.domain.entities.music_search_result import MusicSearchResult, MusicSearchResponse
from src.domain.entities.download_result import DownloadResult
from src.infrastructure.utils.retry_decorator import retry_async

logger = logging.getLogger(__name__)


class MusicSearchService(IMusicSearchService):
    """Music search service implementation using FastSaver API."""

    def __init__(self, cache_repository: IMusicCacheRepository = None):
        self.api_base_url = "https://fastsaverapi.com/search-music"
        self.api_token = "lxcMy0OtNaimyGEQkdHjXAmC"
        self.cache_repository = cache_repository

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def search_music(self, query: str, page: int = 1) -> MusicSearchResponse:
        """Search for music using the FastSaver API."""
        try:
            logger.info(f"Searching music with query: {query}, page: {page}")

            # Prepare API request
            params = {
                "query": query,
                "page": page,
                "token": self.api_token
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(self.api_base_url, params=params)
                response.raise_for_status()

                data = response.json()
                
                # Check for API errors
                if data.get("error", False):
                    logger.error(f"API returned error for query: {query}")
                    return MusicSearchResponse(
                        error=True,
                        page=page,
                        results=[]
                    )

                # Convert API response to domain entities and cache them
                results = []
                for item in data.get("results", []):
                    try:
                        result = MusicSearchResult(
                            title=item.get("title", ""),
                            shortcode=item.get("shortcode", ""),
                            duration=item.get("duration", ""),
                            thumb=item.get("thumb", ""),
                            thumb_best=item.get("thumb_best", "")
                        )
                        results.append(result)

                        # Cache the result if cache repository is available
                        if self.cache_repository:
                            try:
                                await self.cache_repository.save_search_result(result.shortcode, result)
                            except Exception as e:
                                logger.warning(f"Failed to cache result for {result.shortcode}: {e}")

                    except ValueError as e:
                        logger.warning(f"Skipping invalid search result: {e}")
                        continue

                return MusicSearchResponse(
                    error=False,
                    page=data.get("page", page),
                    results=results
                )

        except httpx.RequestError as e:
            logger.error(f"Request error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )
        except Exception as e:
            logger.error(f"Unexpected error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def download_from_shortcode(self, shortcode: str, media_type: str = "audio", bot_username: str = "instasaver_bot") -> DownloadResult:
        """Download media from YouTube using FastSaver API."""
        try:
            # First check cache for title and duration
            cached_result = None
            if self.cache_repository:
                try:
                    cached_result = await self.cache_repository.get_cached_result(shortcode)
                    if cached_result:
                        logger.info(f"Using cached metadata for shortcode: {shortcode}")
                except Exception as e:
                    logger.warning(f"Failed to get cached result: {e}")

            # Use FastSaver API to download
            download_url = "https://fastsaverapi.com/download"

            # Determine format based on media_type
            format_param = "mp3" if media_type == "audio" else "mp4"

            params = {
                "video_id": shortcode,
                "format": format_param,
                "bot_username": bot_username,
                "token": self.api_token
            }

            logger.info(f"Downloading from FastSaver API: {shortcode}, format: {format_param}")

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(download_url, params=params)
                response.raise_for_status()

                data = response.json()

                # Check for API errors
                if data.get("error", False):
                    api_error_msg = data.get('message', 'Unknown error')
                    logger.error(f"FastSaver API returned error for shortcode {shortcode}: {api_error_msg}")
                    return DownloadResult(
                        success=False,
                        message="Download service returned an error",
                        title=None
                    )

                # Extract file_id from response
                file_id = data.get("file_id")
                if not file_id:
                    logger.error(f"No file_id returned from FastSaver API for shortcode {shortcode}")
                    return DownloadResult(
                        success=False,
                        message="Download service did not return valid file",
                        title=None
                    )

                # Get title and duration from cached result or use shortcode as fallback
                title = None
                duration_seconds = None

                if cached_result:
                    title = cached_result.title
                    # Clean up title - remove any problematic characters
                    if title:
                        try:
                            # Ensure proper encoding
                            if isinstance(title, str):
                                # Remove any control characters and normalize
                                title = ''.join(char for char in title if ord(char) >= 32 or char in '\n\r\t')
                                title = title.strip()
                        except Exception as e:
                            logger.warning(f"Error cleaning cached title: {e}")
                            title = None
                    # Convert duration string to seconds if available
                    if cached_result.duration:
                        try:
                            # Handle formats like "3:45" or "1:23:45"
                            time_parts = cached_result.duration.split(":")
                            if len(time_parts) == 2:  # MM:SS
                                duration_seconds = int(time_parts[0]) * 60 + int(time_parts[1])
                            elif len(time_parts) == 3:  # HH:MM:SS
                                duration_seconds = int(time_parts[0]) * 3600 + int(time_parts[1]) * 60 + int(time_parts[2])
                            else:
                                duration_seconds = int(cached_result.duration) if cached_result.duration.isdigit() else None
                        except (ValueError, AttributeError):
                            duration_seconds = None

                # If no title from cache, try to get from YouTube page
                if not title:
                    logger.info("No title from cache, trying to get from YouTube page")
                    title = await self._get_video_title_from_youtube(shortcode)
                    logger.info(f"Title from YouTube page: {repr(title)}")

                # If still no title, use shortcode as fallback
                if not title:
                    title = f"YouTube Media {shortcode}"

                # If no duration from cache, try to get from YouTube page
                if duration_seconds is None:
                    logger.info("No duration from cache, trying to get from YouTube page")
                    duration_seconds = await self._get_video_duration_from_youtube(shortcode)
                    logger.info(f"Duration from YouTube page: {duration_seconds} seconds")

                logger.info(f"FastSaver download successful: file_id={file_id}, title: {title}, duration: {duration_seconds}")

                return DownloadResult(
                    success=True,
                    message="Media downloaded successfully via FastSaver API",
                    file_path=None,  # No local file path since we use file_id
                    title=title,
                    duration=duration_seconds,  # Include duration in seconds
                    telegram_file_id=file_id
                )

        except httpx.RequestError as e:
            logger.error(f"Request error during FastSaver download for shortcode {shortcode}: {e}")
            return DownloadResult(
                success=False,
                message="Network connection error occurred",
                title=None
            )
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error during FastSaver download for shortcode {shortcode}: {e.response.status_code} - {e}")
            return DownloadResult(
                success=False,
                message="Download service temporarily unavailable",
                title=None
            )
        except Exception as e:
            logger.error(f"Unexpected error during FastSaver download for shortcode {shortcode}: {e}")
            return DownloadResult(
                success=False,
                message="Download failed due to unexpected error",
                title=None
            )

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def _get_video_title_from_youtube(self, video_id: str) -> Optional[str]:
        """Get video title from YouTube page."""
        try:
            # Try to get title from YouTube page
            url = f"https://www.youtube.com/watch?v={video_id}"
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url)
                response.raise_for_status()

                # Extract title from page HTML
                html = response.text

                # Try HTML title tag first (most reliable for Cyrillic)
                title_match = re.search(r'<title>([^<]+)</title>', html)
                if title_match:
                    title = title_match.group(1)
                    # Remove " - YouTube" suffix
                    title = title.replace(' - YouTube', '')
                    logger.info(f"Extracted title from HTML title tag: {repr(title)}")
                    return title

                # Fallback: try videoDetails JSON pattern
                title_match = re.search(r'"videoDetails":{[^}]*"title":"([^"]+)"', html)
                if title_match:
                    title = title_match.group(1)
                    logger.info(f"Extracted title from videoDetails: {repr(title)}")
                    return title

                # Last fallback: try basic JSON title pattern
                title_match = re.search(r'"title":"([^"]+)"', html)
                if title_match:
                    title = title_match.group(1)
                    logger.info(f"Extracted title from JSON pattern: {repr(title)}")
                    return title

        except Exception as e:
            logger.warning(f"Failed to get video title for {video_id}: {e}")

        return None  # Return None if title not found

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def _get_video_duration_from_youtube(self, video_id: str) -> Optional[int]:
        """Get video duration in seconds from YouTube page."""
        try:
            # Try to get duration from YouTube page
            url = f"https://www.youtube.com/watch?v={video_id}"
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url)
                response.raise_for_status()

                # Extract duration from page HTML
                html = response.text

                # Try to find duration in videoDetails JSON
                duration_match = re.search(r'"videoDetails":{[^}]*"lengthSeconds":"(\d+)"', html)
                if duration_match:
                    duration_seconds = int(duration_match.group(1))
                    logger.info(f"Extracted duration from videoDetails: {duration_seconds} seconds")
                    return duration_seconds

                # Fallback: try to find duration in other JSON patterns
                duration_match = re.search(r'"lengthSeconds":"(\d+)"', html)
                if duration_match:
                    duration_seconds = int(duration_match.group(1))
                    logger.info(f"Extracted duration from lengthSeconds: {duration_seconds} seconds")
                    return duration_seconds

                # Another fallback: try to find duration in approxDurationMs
                duration_match = re.search(r'"approxDurationMs":"(\d+)"', html)
                if duration_match:
                    duration_ms = int(duration_match.group(1))
                    duration_seconds = duration_ms // 1000
                    logger.info(f"Extracted duration from approxDurationMs: {duration_seconds} seconds")
                    return duration_seconds

        except Exception as e:
            logger.warning(f"Failed to get video duration for {video_id}: {e}")

        return None  # Return None if duration not found
