2025-06-27 10:21:47,651 - src.presentation.controllers.media_controller - INFO - Received download request for URL: https://youtu.be/RUhfTpUQ8eo?si=eaXHN7lmC2Hu5mCW
2025-06-27 10:21:48,235 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/getMe "HTTP/1.1 200 OK"
2025-06-27 10:21:48,239 - src.infrastructure.services.telegram_service - INFO - Bot username retrieved: stillarimai_bot
2025-06-27 10:21:48,240 - src.infrastructure.services.youtube_service - INFO - Downloading via FastSaver API: RUhfTpUQ8eo, format: mp3
2025-06-27 10:21:48,240 - src.infrastructure.services.youtube_service - INFO - Trying format: mp3
2025-06-27 10:21:49,250 - httpx - INFO - HTTP Request: GET https://fastsaverapi.com/download?video_id=RUhfTpUQ8eo&format=mp3&bot_username=stillarimai_bot&token=lxcMy0OtNaimyGEQkdHjXAmC "HTTP/1.1 200 OK"
2025-06-27 10:21:49,251 - src.infrastructure.services.youtube_service - INFO - Raw title from FastSaver API: None
2025-06-27 10:21:49,251 - src.infrastructure.services.youtube_service - INFO - No title from API, trying to get from YouTube page
2025-06-27 10:21:50,070 - httpx - INFO - HTTP Request: GET https://www.youtube.com/watch?v=RUhfTpUQ8eo "HTTP/1.1 200 OK"
2025-06-27 10:21:51,337 - src.infrastructure.services.youtube_service - INFO - Title from YouTube page: 'The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K'
2025-06-27 10:21:51,338 - src.infrastructure.services.youtube_service - INFO - FastSaver download successful: file_id=CQACAgIAAxkDAAEGlPhoXiptiaGFdvkej_XUWt7soVP7HgACF6AAAuar8UoIYK6KmZm_dx4E, format: mp3, returned_media_type: audio, title: The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K, duration: None
2025-06-27 10:21:51,527 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/sendAudio "HTTP/1.1 200 OK"
2025-06-27 10:21:51,532 - src.infrastructure.services.telegram_service - INFO - Audio sent by file_id successfully, new file_id: CQACAgIAAxkBAAICI2hdoJCfTnZf_hrtyZhH7nzuYNM0AAIXoAAC5qvxSjwCfw3imGrtNgQ
2025-06-27 10:21:51,542 - src.infrastructure.repositories.sqlite_history_repository - INFO - Saved download history for user 2105729169
2025-06-27 10:21:52,571 - src.presentation.controllers.media_controller - INFO - Received download request for URL: https://youtu.be/RUhfTpUQ8eo?si=eaXHN7lmC2Hu5mCW
2025-06-27 10:21:53,074 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/getMe "HTTP/1.1 200 OK"
2025-06-27 10:21:53,078 - src.infrastructure.services.telegram_service - INFO - Bot username retrieved: stillarimai_bot
2025-06-27 10:21:53,079 - src.infrastructure.services.youtube_service - INFO - Downloading via FastSaver API: RUhfTpUQ8eo, format: mp3
2025-06-27 10:21:53,079 - src.infrastructure.services.youtube_service - INFO - Trying format: mp3
2025-06-27 10:21:53,679 - httpx - INFO - HTTP Request: GET https://fastsaverapi.com/download?video_id=RUhfTpUQ8eo&format=mp3&bot_username=stillarimai_bot&token=lxcMy0OtNaimyGEQkdHjXAmC "HTTP/1.1 200 OK"
2025-06-27 10:21:53,682 - src.infrastructure.services.youtube_service - INFO - Raw title from FastSaver API: None
2025-06-27 10:21:53,683 - src.infrastructure.services.youtube_service - INFO - No title from API, trying to get from YouTube page
2025-06-27 10:21:54,335 - httpx - INFO - HTTP Request: GET https://www.youtube.com/watch?v=RUhfTpUQ8eo "HTTP/1.1 200 OK"
2025-06-27 10:21:55,044 - src.infrastructure.services.youtube_service - INFO - Title from YouTube page: 'The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K'
2025-06-27 10:21:55,044 - src.infrastructure.services.youtube_service - INFO - FastSaver download successful: file_id=CQACAgIAAxkDAAEGlP9oXipxRmMk6XEk-WmCGuuncvGQNAACF6AAAuar8UoIYK6KmZm_dx4E, format: mp3, returned_media_type: audio, title: The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K, duration: None
2025-06-27 10:21:55,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/sendAudio "HTTP/1.1 200 OK"
2025-06-27 10:21:55,282 - src.infrastructure.services.telegram_service - INFO - Audio sent by file_id successfully, new file_id: CQACAgIAAxkBAAICI2hdoJCfTnZf_hrtyZhH7nzuYNM0AAIXoAAC5qvxSjwCfw3imGrtNgQ
2025-06-27 10:21:55,289 - src.infrastructure.repositories.sqlite_history_repository - INFO - Saved download history for user 2105729169
2025-06-27 10:21:56,000 - src.presentation.controllers.media_controller - INFO - Received download request for URL: https://youtu.be/RUhfTpUQ8eo?si=eaXHN7lmC2Hu5mCW
2025-06-27 10:21:56,808 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/getMe "HTTP/1.1 200 OK"
2025-06-27 10:21:56,811 - src.infrastructure.services.telegram_service - INFO - Bot username retrieved: stillarimai_bot
2025-06-27 10:21:56,811 - src.infrastructure.services.youtube_service - INFO - Downloading via FastSaver API: RUhfTpUQ8eo, format: mp3
2025-06-27 10:21:56,811 - src.infrastructure.services.youtube_service - INFO - Trying format: mp3
2025-06-27 10:21:57,698 - httpx - INFO - HTTP Request: GET https://fastsaverapi.com/download?video_id=RUhfTpUQ8eo&format=mp3&bot_username=stillarimai_bot&token=lxcMy0OtNaimyGEQkdHjXAmC "HTTP/1.1 200 OK"
2025-06-27 10:21:57,702 - src.infrastructure.services.youtube_service - INFO - Raw title from FastSaver API: None
2025-06-27 10:21:57,702 - src.infrastructure.services.youtube_service - INFO - No title from API, trying to get from YouTube page
2025-06-27 10:21:58,584 - httpx - INFO - HTTP Request: GET https://www.youtube.com/watch?v=RUhfTpUQ8eo "HTTP/1.1 200 OK"
2025-06-27 10:21:59,593 - src.presentation.controllers.media_controller - INFO - Received download request for URL: https://youtu.be/RUhfTpUQ8eo?si=eaXHN7lmC2Hu5mCW
2025-06-27 10:22:01,339 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/getMe "HTTP/1.1 200 OK"
2025-06-27 10:22:01,343 - src.infrastructure.services.telegram_service - INFO - Bot username retrieved: stillarimai_bot
2025-06-27 10:22:01,343 - src.infrastructure.services.youtube_service - INFO - Downloading via FastSaver API: RUhfTpUQ8eo, format: mp3
2025-06-27 10:22:01,343 - src.infrastructure.services.youtube_service - INFO - Trying format: mp3
2025-06-27 10:22:02,493 - httpx - INFO - HTTP Request: GET https://fastsaverapi.com/download?video_id=RUhfTpUQ8eo&format=mp3&bot_username=stillarimai_bot&token=lxcMy0OtNaimyGEQkdHjXAmC "HTTP/1.1 200 OK"
2025-06-27 10:22:02,498 - src.infrastructure.services.youtube_service - INFO - Raw title from FastSaver API: None
2025-06-27 10:22:02,500 - src.infrastructure.services.youtube_service - INFO - No title from API, trying to get from YouTube page
2025-06-27 10:22:03,043 - httpx - INFO - HTTP Request: GET https://www.youtube.com/watch?v=RUhfTpUQ8eo "HTTP/1.1 200 OK"
2025-06-27 10:22:03,674 - src.infrastructure.services.youtube_service - INFO - Title from YouTube page: 'The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K'
2025-06-27 10:22:03,675 - src.infrastructure.services.youtube_service - INFO - FastSaver download successful: file_id=CQACAgIAAxkDAAEOMd5oXip6n3NWp7cpU-PqTMHGS27Z_QACF6AAAuar8Up27bjZ_kv6Sx4E, format: mp3, returned_media_type: audio, title: The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K, duration: None
2025-06-27 10:22:03,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/sendAudio "HTTP/1.1 200 OK"
2025-06-27 10:22:03,960 - src.infrastructure.services.telegram_service - INFO - Audio sent by file_id successfully, new file_id: CQACAgIAAxkBAAICI2hdoJCfTnZf_hrtyZhH7nzuYNM0AAIXoAAC5qvxSjwCfw3imGrtNgQ
2025-06-27 10:22:03,965 - src.infrastructure.repositories.sqlite_history_repository - INFO - Saved download history for user 2105729169
2025-06-27 10:22:04,617 - src.presentation.controllers.media_controller - INFO - Received download request for URL: https://youtu.be/RUhfTpUQ8eo?si=eaXHN7lmC2Hu5mCW
2025-06-27 10:22:04,663 - src.infrastructure.services.youtube_service - INFO - Title from YouTube page: 'The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K'
2025-06-27 10:22:04,664 - src.infrastructure.services.youtube_service - INFO - FastSaver download successful: file_id=CQACAgIAAxkDAAEOMdtoXip1rGLNXmbf83kWJTLndVDZ7wACF6AAAuar8Up27bjZ_kv6Sx4E, format: mp3, returned_media_type: audio, title: The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K, duration: None
2025-06-27 10:22:05,020 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/getMe "HTTP/1.1 200 OK"
2025-06-27 10:22:05,024 - src.infrastructure.services.telegram_service - INFO - Bot username retrieved: stillarimai_bot
2025-06-27 10:22:05,024 - src.infrastructure.services.youtube_service - INFO - Downloading via FastSaver API: RUhfTpUQ8eo, format: mp3
2025-06-27 10:22:05,024 - src.infrastructure.services.youtube_service - INFO - Trying format: mp3
2025-06-27 10:22:05,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/sendAudio "HTTP/1.1 200 OK"
2025-06-27 10:22:05,056 - src.infrastructure.services.telegram_service - INFO - Audio sent by file_id successfully, new file_id: CQACAgIAAxkBAAICI2hdoJCfTnZf_hrtyZhH7nzuYNM0AAIXoAAC5qvxSjwCfw3imGrtNgQ
2025-06-27 10:22:05,057 - src.infrastructure.repositories.sqlite_history_repository - INFO - Saved download history for user 2105729169
2025-06-27 10:22:05,513 - httpx - INFO - HTTP Request: GET https://fastsaverapi.com/download?video_id=RUhfTpUQ8eo&format=mp3&bot_username=stillarimai_bot&token=lxcMy0OtNaimyGEQkdHjXAmC "HTTP/1.1 200 OK"
2025-06-27 10:22:05,517 - src.infrastructure.services.youtube_service - INFO - Raw title from FastSaver API: None
2025-06-27 10:22:05,517 - src.infrastructure.services.youtube_service - INFO - No title from API, trying to get from YouTube page
2025-06-27 10:22:05,958 - httpx - INFO - HTTP Request: GET https://www.youtube.com/watch?v=RUhfTpUQ8eo "HTTP/1.1 200 OK"
2025-06-27 10:22:06,565 - src.infrastructure.services.youtube_service - INFO - Title from YouTube page: 'The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K'
2025-06-27 10:22:06,565 - src.infrastructure.services.youtube_service - INFO - FastSaver download successful: file_id=CQACAgIAAxkDAAEOMeJoXip92VXnOdmk7LmGWRQUHrHPCgACF6AAAuar8Up27bjZ_kv6Sx4E, format: mp3, returned_media_type: audio, title: The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K, duration: None
2025-06-27 10:22:06,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/sendAudio "HTTP/1.1 200 OK"
2025-06-27 10:22:06,720 - src.infrastructure.services.telegram_service - INFO - Audio sent by file_id successfully, new file_id: CQACAgIAAxkBAAICI2hdoJCfTnZf_hrtyZhH7nzuYNM0AAIXoAAC5qvxSjwCfw3imGrtNgQ
2025-06-27 10:22:06,722 - src.infrastructure.repositories.sqlite_history_repository - INFO - Saved download history for user 2105729169
2025-06-27 10:22:09,851 - src.presentation.controllers.media_controller - INFO - Received download request for URL: https://youtu.be/RUhfTpUQ8eo?si=eaXHN7lmC2Hu5mCW
2025-06-27 10:22:10,253 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/getMe "HTTP/1.1 200 OK"
2025-06-27 10:22:10,256 - src.infrastructure.services.telegram_service - INFO - Bot username retrieved: stillarimai_bot
2025-06-27 10:22:10,256 - src.infrastructure.services.youtube_service - INFO - Downloading via FastSaver API: RUhfTpUQ8eo, format: mp3
2025-06-27 10:22:10,256 - src.infrastructure.services.youtube_service - INFO - Trying format: mp3
2025-06-27 10:22:11,700 - httpx - INFO - HTTP Request: GET https://fastsaverapi.com/download?video_id=RUhfTpUQ8eo&format=mp3&bot_username=stillarimai_bot&token=lxcMy0OtNaimyGEQkdHjXAmC "HTTP/1.1 200 OK"
2025-06-27 10:22:11,703 - src.infrastructure.services.youtube_service - INFO - Raw title from FastSaver API: None
2025-06-27 10:22:11,704 - src.infrastructure.services.youtube_service - INFO - No title from API, trying to get from YouTube page
2025-06-27 10:22:12,150 - httpx - INFO - HTTP Request: GET https://www.youtube.com/watch?v=RUhfTpUQ8eo "HTTP/1.1 200 OK"
2025-06-27 10:22:12,666 - src.infrastructure.services.youtube_service - INFO - Title from YouTube page: 'The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K'
2025-06-27 10:22:12,666 - src.infrastructure.services.youtube_service - INFO - FastSaver download successful: file_id=CQACAgIAAxkDAAEOMeloXiqD_QtA5vVie5bJvuiD0PMwyAACF6AAAuar8Up27bjZ_kv6Sx4E, format: mp3, returned_media_type: audio, title: The Sharks Return to Finish | Best horror movie | Free full movies on youtube in English 4K, duration: None
2025-06-27 10:22:12,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7862703388:AAFqr36gvUZ9PIMvDYZ9BoJOxYEi5lMC07A/sendAudio "HTTP/1.1 200 OK"
2025-06-27 10:22:12,883 - src.infrastructure.services.telegram_service - INFO - Audio sent by file_id successfully, new file_id: CQACAgIAAxkBAAICI2hdoJCfTnZf_hrtyZhH7nzuYNM0AAIXoAAC5qvxSjwCfw3imGrtNgQ
2025-06-27 10:22:12,885 - src.infrastructure.repositories.sqlite_history_repository - INFO - Saved download history for user 2105729169
